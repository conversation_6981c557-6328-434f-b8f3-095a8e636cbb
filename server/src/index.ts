import swagger from "@elysiajs/swagger";
import { <PERSON>sia } from "elysia";
import { initializeConnection, getDB, isConnected } from "@config/mongo";
// import { createAuth } from "@libs/auth";
import { cors } from "@elysiajs/cors";
import { authMiddleware } from "@middleware/betterAuth";
// import { proxyRouter } from "@app/proxy/proxy.controller";

// Initialize MongoDB connection before starting the server
const startServer = async () => {
	try {
		// Connect to MongoDB
		await initializeConnection();

		// Optional: Get the native DB instance if needed
		const db = getDB();

		// Initialize auth with the established connection
		const authMiddle = authMiddleware(db);

		// Create and start Elysia app
		const app = new Elysia()
			.use(
				cors({
					origin: "*",
					methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
					credentials: true,
					allowedHeaders: ["Content-Type", "Authorization"],
				}),
			)

			.use(swagger())
			.use(authMiddle)
			.get("/api", () => "Hello Elysia")
			.get("/api/db-status", () => {
				return { connected: !!db.databaseName, dbName: db.databaseName };
			})
			.get("/api/user", ({ user }) => user, {
				auth: true,
			})
			// .use(proxyRouter)
			.listen(process.env.APP_PORT || 3010);

		console.log(
			`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`,
		);
	} catch (error) {
		console.error("Failed to start server:", error);
		process.exit(1);
	}
};

// Start the server
startServer();
