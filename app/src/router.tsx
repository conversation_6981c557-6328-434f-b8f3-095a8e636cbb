import { createBrowserRouter, Navigate } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { AuthGuard, PublicRoute } from './lib/auth-guard'
import { LoadingSpinner } from './components/ui/loading-spinner'
import { ResponsiveLayout } from './components/layout/ResponsiveLayout'


// Lazy load page components for code splitting
const LoginPage = lazy(() => import('./pages/auth/LoginPage').then(m => ({ default: m.LoginPage })))
const SignupPage = lazy(() => import('./pages/auth/SignupPage').then(m => ({ default: m.SignupPage })))

// Admin pages
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard').then(m => ({ default: m.AdminDashboard })))
const UserManagementPage = lazy(() => import('./pages/admin/UserManagementPage').then(m => ({ default: m.UserManagementPage })))
const ApiUsageDashboard = lazy(() => import('./pages/admin/ApiUsageDashboard').then(m => ({ default: m.ApiUsageDashboard })))
const AdminSettings = lazy(() => import('./pages/admin/AdminSettings').then(m => ({ default: m.AdminSettings })))

// User pages
const UserDashboard = lazy(() => import('./pages/user/UserDashboard'))
const ApiKeysPage = lazy(() => import('./pages/user/ApiKeysPage'))
const UsageAnalyticsPage = lazy(() => import('./pages/user/UsageAnalyticsPage'))
const ProfilePage = lazy(() => import('./pages/user/ProfilePage'))



// Wrapper component for lazy loading with suspense
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
)

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/login" replace />,
  },
  {
    path: '/login',
    element: (
      <PublicRoute>
        <LazyWrapper>
          <LoginPage />
        </LazyWrapper>
      </PublicRoute>
    ),
  },
  {
    path: '/signup',
    element: (
      <PublicRoute>
        <LazyWrapper>
          <SignupPage />
        </LazyWrapper>
      </PublicRoute>
    ),
  },
  // User area wrapped with responsive layout
  {
    path: '/dashboard',
    element: (
      <AuthGuard requiredRole="user">
        <ResponsiveLayout isAdmin={false} />
      </AuthGuard>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <UserDashboard />
          </LazyWrapper>
        ),
      },
      {
        path: 'api-keys',
        element: (
          <LazyWrapper>
            <ApiKeysPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'analytics',
        element: (
          <LazyWrapper>
            <UsageAnalyticsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'profile',
        element: (
          <LazyWrapper>
            <ProfilePage />
          </LazyWrapper>
        ),
      },
    ],
  },
  // Admin area wrapped with responsive layout
  {
    path: '/admin',
    element: (
      <AuthGuard requiredRole="admin">
        <ResponsiveLayout isAdmin={true} />
      </AuthGuard>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <AdminDashboard />
          </LazyWrapper>
        ),
      },
      // Back-compat route alias for older '/admin/dashboard' links
      {
        path: 'dashboard',
        element: <Navigate to="/admin" replace />,
      },
      {
        path: 'users',
        element: (
          <LazyWrapper>
            <UserManagementPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'api-usage',
        element: (
          <LazyWrapper>
            <ApiUsageDashboard />
          </LazyWrapper>
        ),
      },
      // Alias to support sidebar using '/admin/usage'
      {
        path: 'usage',
        element: <Navigate to="/admin/api-usage" replace />,
      },
      {
        path: 'settings',
        element: (
          <LazyWrapper>
            <AdminSettings />
          </LazyWrapper>
        ),
      },
    ],
  },

  {
    path: '*',
    element: <Navigate to="/login" replace />,
  },
])