import { RouterProvider } from 'react-router-dom'
import { Provider } from 'jotai'
import { router } from './router'
import ErrorBoundary from './components/ErrorBoundary'
import { useSessionMonitoring } from './hooks/useSessionMonitoring'

// Component to handle session monitoring
function AppWithSessionMonitoring() {
  useSessionMonitoring() // This will monitor localStorage changes and validate sessions
  
  return <RouterProvider router={router} />
}

function App() {
  return (
    <ErrorBoundary>
      <Provider>
        <AppWithSessionMonitoring />
      </Provider>
    </ErrorBoundary>
  )
}

export default App
